#!/bin/bash

# 一键修复财务系统部署问题脚本
# 解决数据库连接和容器启动问题

echo "🚀 财务系统一键修复脚本"
echo "=========================="

# 服务器信息
TARGET_SERVER="10.25.1.85"
SSH_USER="admin"

echo "📋 目标服务器: $TARGET_SERVER"
echo "📋 SSH用户: $SSH_USER"
echo ""

# 修复函数
fix_deployment() {
    echo "🔧 开始修复部署问题..."
    
    ssh $SSH_USER@$TARGET_SERVER << 'EOF'
        echo "📋 步骤1: 停止所有相关容器"
        docker stop financial-backend financial-mysql financial-nginx 2>/dev/null || true
        
        echo "📋 步骤2: 清理旧容器但保留数据"
        docker rm financial-backend 2>/dev/null || true
        # 保留 financial-mysql 和 financial-nginx
        
        echo "📋 步骤3: 确保数据库正常运行"
        docker start financial-mysql
        sleep 10
        
        echo "📋 步骤4: 重新配置数据库"
        docker exec financial-mysql mysql -uroot -p'Zlb&198838' -e "
            CREATE DATABASE IF NOT EXISTS overdue_debt_db;
            CREATE DATABASE IF NOT EXISTS user_system;
            CREATE DATABASE IF NOT EXISTS kingdee;
            GRANT ALL PRIVILEGES ON *.* TO 'root'@'%';
            FLUSH PRIVILEGES;
        " 2>/dev/null || echo "数据库配置跳过"
        
        echo "📋 步骤5: 重新创建后端容器（使用正确配置）"
        docker run -d \
          --name financial-backend \
          --network financial-network \
          -p 8080:8080 \
          -e SPRING_PROFILES_ACTIVE=production,docker \
          -e SPRING_DATASOURCE_PRIMARY_URL='***********************************************************************************************************************************************************************************' \
          -e SPRING_DATASOURCE_PRIMARY_USERNAME=root \
          -e SPRING_DATASOURCE_PRIMARY_PASSWORD='Zlb&198838' \
          -e SPRING_DATASOURCE_USER_SYSTEM_URL='*************************************************************************************************************************************************************************************************************' \
          -e SPRING_DATASOURCE_USER_SYSTEM_USERNAME=root \
          -e SPRING_DATASOURCE_USER_SYSTEM_PASSWORD='Zlb&198838' \
          -e SPRING_DATASOURCE_SECONDARY_URL='***************************************************************************************************************************************************************************' \
          -e SPRING_DATASOURCE_SECONDARY_USERNAME=root \
          -e SPRING_DATASOURCE_SECONDARY_PASSWORD='Zlb&198838' \
          --restart unless-stopped \
          openjdk:21-jdk sh -c '
            echo "等待数据库完全启动..."
            while ! nc -z financial-mysql 3306 2>/dev/null; do 
                echo "等待数据库..."
                sleep 5
            done
            echo "数据库已就绪，启动应用..."
            java -Xms512m -Xmx1024m -jar /app/api-gateway-1.0-SNAPSHOT.jar --spring.profiles.active=production
          '
        
        echo "📋 步骤6: 确保前端服务运行"
        docker start financial-nginx 2>/dev/null || true
        
        echo "📋 步骤7: 等待服务启动"
        sleep 30
        
        echo "📋 步骤8: 检查最终状态"
        docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'
        
        echo ""
        echo "📋 步骤9: 测试服务可用性"
        curl -s --connect-timeout 5 http://localhost/ | head -1 && echo "✅ 前端服务正常" || echo "❌ 前端服务异常"
        curl -s --connect-timeout 5 http://localhost:8080/actuator/health | head -1 && echo "✅ 后端服务正常" || echo "⏳ 后端服务启动中"
EOF

    echo ""
    echo "🎯 修复完成！"
    echo "访问地址："
    echo "  前端: http://$TARGET_SERVER/"
    echo "  后端: http://$TARGET_SERVER:8080/"
    echo "  健康检查: http://$TARGET_SERVER:8080/actuator/health"
}

# 快速诊断函数
quick_status() {
    echo "🔍 快速状态检查..."
    ssh $SSH_USER@$TARGET_SERVER << 'EOF'
        echo "容器状态:"
        docker ps --format 'table {{.Names}}\t{{.Status}}'
        
        echo ""
        echo "服务测试:"
        curl -s --connect-timeout 3 http://localhost/ > /dev/null && echo "✅ 前端正常" || echo "❌ 前端异常"
        curl -s --connect-timeout 3 http://localhost:8080/actuator/health > /dev/null && echo "✅ 后端正常" || echo "⏳ 后端启动中"
EOF
}

# 主菜单
case "${1:-menu}" in
    "fix")
        fix_deployment
        ;;
    "status")
        quick_status
        ;;
    "menu"|*)
        echo "使用方法:"
        echo "  $0 fix     - 一键修复部署问题"
        echo "  $0 status  - 快速检查状态"
        echo ""
        echo "或者直接运行修复:"
        read -p "是否立即执行修复? (y/N): " confirm
        if [[ $confirm =~ ^[Yy]$ ]]; then
            fix_deployment
        else
            echo "取消操作"
        fi
        ;;
esac