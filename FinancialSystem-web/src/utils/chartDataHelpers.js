/**
 * Chart.js 数据处理辅助函数
 * 提供安全的数据访问和格式化功能
 */

/**
 * 安全地从Chart.js context中获取数据值
 * @param {Object} context - Chart.js的context对象
 * @returns {number|null} 数据值或null
 */
export const getChartDataValue = (context) => {
  try {
    // 优先尝试从dataset.data中获取
    const datasetData = context.dataset?.data;
    const index = context.dataIndex;
    
    if (!datasetData || index == null || index < 0 || index >= datasetData.length) {
      return null;
    }
    
    const dataPoint = datasetData[index];
    
    // 处理不同的数据格式
    if (typeof dataPoint === 'object' && dataPoint !== null) {
      // 对象格式 {x, y} 或 {label, value}
      return dataPoint.y ?? dataPoint.value ?? null;
    } else if (typeof dataPoint === 'number') {
      // 直接是数字
      return dataPoint;
    }
    
    return null;
  } catch (error) {
    console.warn('Failed to get chart data value:', error);
    return null;
  }
};

/**
 * 验证并格式化图表数据
 * @param {Array} data - 原始数据数组
 * @param {Object} options - 格式化选项
 * @returns {Object} 格式化后的图表数据
 */
export const formatChartData = (data, options = {}) => {
  const {
    labelKey = 'label',
    valueKey = 'value',
    defaultValue = 0
  } = options;
  
  if (!Array.isArray(data)) {
    console.warn('Chart data is not an array:', data);
    return { labels: [], values: [] };
  }
  
  const labels = [];
  const values = [];
  
  data.forEach(item => {
    if (item && typeof item === 'object') {
      const label = item[labelKey] || '未知';
      const value = parseFloat(item[valueKey]) || defaultValue;
      
      labels.push(label);
      values.push(value);
    }
  });
  
  return { labels, values };
};

/**
 * 创建安全的datalabels配置
 * @param {Object} customOptions - 自定义配置选项
 * @returns {Object} datalabels配置对象
 */
export const createSafeDatalabelsConfig = (customOptions = {}) => {
  return {
    display: function(context) {
      try {
        const value = getChartDataValue(context);
        return value != null && !isNaN(value) && value > 0;
      } catch (error) {
        console.warn('Datalabels display check error:', error);
        return false;
      }
    },
    formatter: function(value, context) {
      try {
        const actualValue = getChartDataValue(context);
        
        if (actualValue == null || isNaN(actualValue)) {
          return '';
        }
        
        // 使用自定义格式化函数或默认格式
        if (customOptions.formatter) {
          return customOptions.formatter(actualValue, context);
        }
        
        return Math.round(actualValue).toString();
      } catch (error) {
        console.error('Datalabels formatter error:', error);
        return '';
      }
    },
    anchor: customOptions.anchor || 'end',
    align: customOptions.align || 'start',
    offset: customOptions.offset ?? -5,
    color: customOptions.color || '#000',
    font: customOptions.font || {
      size: 11,
      weight: 'bold'
    },
    ...customOptions
  };
};

/**
 * 创建安全的tooltip回调配置
 * @param {Object} options - 配置选项
 * @returns {Object} tooltip callbacks配置
 */
export const createSafeTooltipCallbacks = (options = {}) => {
  const { unit = '万元', decimals = 2 } = options;
  
  return {
    label: function(context) {
      try {
        const label = context.dataset.label || '';
        const value = getChartDataValue(context);
        
        if (value == null || isNaN(value)) {
          return `${label}: 0.00 ${unit}`;
        }
        
        return `${label}: ${value.toFixed(decimals)} ${unit}`;
      } catch (error) {
        console.error('Tooltip label error:', error);
        return '数据加载错误';
      }
    }
  };
};

/**
 * 验证图表数据的完整性
 * @param {Array} data - 数据数组
 * @param {Array} requiredFields - 必需字段
 * @returns {boolean} 数据是否有效
 */
export const validateChartData = (data, requiredFields = []) => {
  if (!Array.isArray(data) || data.length === 0) {
    return false;
  }
  
  return data.every(item => {
    if (!item || typeof item !== 'object') {
      return false;
    }
    
    return requiredFields.every(field => {
      const value = item[field];
      return value !== undefined && value !== null;
    });
  });
};

const chartDataHelpers = {
  getChartDataValue,
  formatChartData,
  createSafeDatalabelsConfig,
  createSafeTooltipCallbacks,
  validateChartData
};

export default chartDataHelpers;