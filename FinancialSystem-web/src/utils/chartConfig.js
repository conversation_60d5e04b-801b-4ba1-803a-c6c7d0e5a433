/**
 * Chart.js 全局配置文件
 * 统一注册所有需要的Chart.js组件，避免"controller not registered"错误
 */

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  LogarithmicScale,
  RadialLinearScale,
  TimeScale,
  TimeSeriesScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  // 所有控制器
  BarController,
  LineController,
  DoughnutController,
  PieController,
  PolarAreaController,
  RadarController,
  ScatterController,
  BubbleController,
  // 插件
  Legend,
  Tooltip,
  Title,
  SubTitle,
  Filler,
} from 'chart.js';

// 导入错误处理器
import './chartErrorHandler.js';

// 注册所有Chart.js组件
ChartJS.register(
  // 比例尺
  CategoryScale,
  LinearScale,
  LogarithmicScale,
  RadialLinearScale,
  TimeScale,
  TimeSeriesScale,

  // 元素
  BarElement,
  LineElement,
  PointElement,
  ArcElement,

  // 控制器
  BarController,
  <PERSON><PERSON>ontroller,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Radar<PERSON>ontroll<PERSON>,
  <PERSON>atter<PERSON><PERSON><PERSON><PERSON>,
  B<PERSON>bleController,

  // 插件
  Legend,
  Tooltip,
  Title,
  SubTitle,
  Filler,
);

// 添加全局错误处理
const originalConsoleError = console.error;
console.error = function(...args) {
  // 过滤掉Chart.js的已知错误
  const message = args.join(' ');
  if (message.includes('Cannot read properties of undefined (reading \'y\')') ||
      message.includes('Chart.js') ||
      message.includes('datalabels')) {
    console.warn('Chart.js error intercepted:', ...args);
    return;
  }
  originalConsoleError.apply(console, args);
};

// 设置全局默认配置
ChartJS.defaults.responsive = true;
ChartJS.defaults.maintainAspectRatio = false;

// 全局错误处理
ChartJS.defaults.plugins.datalabels = {
  display: function(context) {
    try {
      // 安全检查数据值
      const value = context.dataset?.data?.[context.dataIndex];
      return value != null && !isNaN(value);
    } catch (error) {
      console.warn('Chart.js datalabels display error:', error);
      return false;
    }
  },
  formatter: function(value, context) {
    try {
      if (value == null || isNaN(value)) {
        return '';
      }
      return value.toString();
    } catch (error) {
      console.error('Chart.js datalabels formatter error:', error);
      return '';
    }
  }
};

// 全局tooltip错误处理
ChartJS.defaults.plugins.tooltip = {
  ...ChartJS.defaults.plugins.tooltip,
  callbacks: {
    label: function(context) {
      try {
        const label = context.dataset.label || '';
        const value = context.parsed?.y ?? context.parsed ?? 0;
        if (isNaN(value)) {
          return `${label}: 0`;
        }
        return `${label}: ${value}`;
      } catch (error) {
        console.error('Chart.js tooltip error:', error);
        return '数据错误';
      }
    }
  }
};

export default ChartJS;
