/**
 * Chart.js 错误处理器
 * 专门处理Chart.js运行时错误，特别是datalabels插件的错误
 */

// 保存原始的错误处理函数
const originalWindowError = window.onerror;
const originalUnhandledRejection = window.onunhandledrejection;

// Chart.js错误模式
const CHART_ERROR_PATTERNS = [
  /Cannot read properties of undefined \(reading 'y'\)/,
  /Cannot read properties of undefined \(reading 'x'\)/,
  /Cannot read properties of null \(reading 'y'\)/,
  /Cannot read properties of null \(reading 'x'\)/,
  /chart\.js/i,
  /datalabels/i,
  /chartjs/i
];

/**
 * 检查是否为Chart.js相关错误
 * @param {string} message - 错误消息
 * @returns {boolean}
 */
function isChartJSError(message) {
  return CHART_ERROR_PATTERNS.some(pattern => pattern.test(message));
}

/**
 * 安全的错误日志记录
 * @param {string} type - 错误类型
 * @param {Error|string} error - 错误对象或消息
 */
function logChartError(type, error) {
  console.warn(`[Chart.js ${type}]`, error);
  
  // 可以在这里添加错误上报逻辑
  // 例如发送到错误监控服务
}

/**
 * 全局错误处理器
 */
window.onerror = function(message, source, lineno, colno, error) {
  if (typeof message === 'string' && isChartJSError(message)) {
    logChartError('Runtime Error', {
      message,
      source,
      lineno,
      colno,
      error
    });
    return true; // 阻止默认错误处理
  }
  
  // 调用原始错误处理器
  if (originalWindowError) {
    return originalWindowError.call(this, message, source, lineno, colno, error);
  }
  
  return false;
};

/**
 * Promise错误处理器
 */
window.onunhandledrejection = function(event) {
  const reason = event.reason;
  const message = reason?.message || reason?.toString() || '';
  
  if (isChartJSError(message)) {
    logChartError('Promise Rejection', reason);
    event.preventDefault(); // 阻止默认处理
    return;
  }
  
  // 调用原始处理器
  if (originalUnhandledRejection) {
    return originalUnhandledRejection.call(this, event);
  }
};

/**
 * 拦截console.error中的Chart.js错误
 */
const originalConsoleError = console.error;
console.error = function(...args) {
  const message = args.join(' ');
  
  if (isChartJSError(message)) {
    logChartError('Console Error', args);
    return;
  }
  
  // 调用原始console.error
  originalConsoleError.apply(console, args);
};

/**
 * Chart.js插件错误包装器
 */
export function wrapChartPlugin(plugin) {
  if (!plugin || typeof plugin !== 'object') {
    return plugin;
  }
  
  const wrappedPlugin = { ...plugin };
  
  // 包装插件的各个生命周期方法
  const lifecycleMethods = [
    'beforeInit', 'afterInit',
    'beforeUpdate', 'afterUpdate',
    'beforeDatasetUpdate', 'afterDatasetUpdate',
    'beforeRender', 'afterRender',
    'beforeDraw', 'afterDraw'
  ];
  
  lifecycleMethods.forEach(method => {
    if (typeof plugin[method] === 'function') {
      wrappedPlugin[method] = function(...args) {
        try {
          return plugin[method].apply(this, args);
        } catch (error) {
          logChartError(`Plugin ${method}`, error);
          return null;
        }
      };
    }
  });
  
  return wrappedPlugin;
}

/**
 * 安全的Chart.js数据处理
 */
export function safeChartData(data) {
  if (!data || typeof data !== 'object') {
    return { labels: [], datasets: [] };
  }
  
  const safeData = { ...data };
  
  // 确保labels存在且为数组
  if (!Array.isArray(safeData.labels)) {
    safeData.labels = [];
  }
  
  // 确保datasets存在且为数组
  if (!Array.isArray(safeData.datasets)) {
    safeData.datasets = [];
  }
  
  // 处理每个dataset
  safeData.datasets = safeData.datasets.map(dataset => {
    if (!dataset || typeof dataset !== 'object') {
      return { data: [] };
    }
    
    const safeDataset = { ...dataset };
    
    // 确保data存在且为数组
    if (!Array.isArray(safeDataset.data)) {
      safeDataset.data = [];
    }
    
    // 处理数据点
    safeDataset.data = safeDataset.data.map(point => {
      if (typeof point === 'number') {
        return isNaN(point) ? 0 : point;
      } else if (point && typeof point === 'object') {
        const safePoint = { ...point };
        if (typeof safePoint.x === 'number' && isNaN(safePoint.x)) {
          safePoint.x = 0;
        }
        if (typeof safePoint.y === 'number' && isNaN(safePoint.y)) {
          safePoint.y = 0;
        }
        return safePoint;
      } else {
        return 0;
      }
    });
    
    return safeDataset;
  });
  
  return safeData;
}

/**
 * 安全的Chart.js选项处理
 */
export function safeChartOptions(options) {
  if (!options || typeof options !== 'object') {
    return {};
  }
  
  const safeOptions = { ...options };
  
  // 确保plugins存在
  if (!safeOptions.plugins) {
    safeOptions.plugins = {};
  }
  
  // 包装datalabels配置
  if (safeOptions.plugins.datalabels) {
    const datalabels = safeOptions.plugins.datalabels;
    
    if (typeof datalabels.display === 'function') {
      const originalDisplay = datalabels.display;
      datalabels.display = function(context) {
        try {
          return originalDisplay.call(this, context);
        } catch (error) {
          logChartError('Datalabels Display', error);
          return false;
        }
      };
    }
    
    if (typeof datalabels.formatter === 'function') {
      const originalFormatter = datalabels.formatter;
      datalabels.formatter = function(value, context) {
        try {
          return originalFormatter.call(this, value, context);
        } catch (error) {
          logChartError('Datalabels Formatter', error);
          return '';
        }
      };
    }
  }
  
  return safeOptions;
}

// 导出错误处理器
export default {
  wrapChartPlugin,
  safeChartData,
  safeChartOptions,
  isChartJSError,
  logChartError
};
