import React from 'react';
import PropTypes from 'prop-types';
import { Box, Typography, Button } from '@mui/material';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError() {
    // 更新状态以显示错误界面
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // 记录错误详情
    // eslint-disable-next-line no-console
    console.error('React Error Boundary caught an error:', error, errorInfo);
    this.setState({
      error: error,
      errorInfo: errorInfo,
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight="100vh"
          p={3}
        >
          <Typography variant="h4" color="error" mb={2}>
            应用程序出现错误
          </Typography>
          <Typography variant="body1" color="text.primary" mb={3} textAlign="center">
            抱歉，应用程序遇到了意外错误。请刷新页面重试。
          </Typography>
          <Button variant="contained" color="primary" onClick={() => window.location.reload()} sx={{ mb: 2 }}>
            刷新页面
          </Button>
          <details style={{ marginTop: '20px', whiteSpace: 'pre-wrap' }}>
            <summary>错误详情</summary>
            <p>{this.state.error?.toString() || '无错误信息'}</p>
            <p>{this.state.errorInfo?.componentStack || '无组件堆栈信息'}</p>
          </details>
        </Box>
      );
    }

    return this.props.children;
  }
}

ErrorBoundary.propTypes = {
  children: PropTypes.node.isRequired,
};

export default ErrorBoundary;
